# ZUI Component Library Usage Analysis

## Overview
This document provides a comprehensive analysis of all components and functions being used from `@zscaler/zui-component-library` in the `packages/adaptive-access/pages` directory.

**Analysis Date:** January 7, 2025  
**Package Version:** @zscaler/zui-component-library ^3.2.1  
**Total Imports:** 46 distinct imports

---

## Summary Statistics

| Category | Count |
|----------|-------|
| UI Components | 26 |
| Utility Functions | 19 |
| SCSS Imports | 1 |
| **Total** | **46** |

---

## UI Components (26)

### Core Form Components
- **Button** - Used throughout forms, actions, and query builder
- **Input** - Used in various forms (conditional access, subject identifier, context type)
- **TextArea** - Used in conditional access profile forms
- **PasswordInput** - Used in integration modals (SilverFort, CrowdStrike, Microsoft Defender)
- **Field** - Used in forms and filters, subject identifier and context type detail forms
- **FieldGroup** - Used in signal history filters
- **Search** - Used in signal history page, conditional access profiles, and override manager

### Date and Selection Components
- **CalendarDropDown** - Used in signal history filters
- **DropDown** - Used in filters, forms, query builder, and common actions
- **InlineDatePicker** - Used in subject identifier and context type detail forms
- **ToggleButton** - Used in integration modals

### Modal Components
- **Modal** - Used in integration modals and override manager components
- **ModalBody** - Used in modal components across integrations and override manager
- **ModalFooter** - Used in integration modals and subject identifier detail containers
- **ModalHeader** - Used in modal components across integrations and override manager
- **CRUDModal** - Used in conditional access profiles and context type detail containers

### Table Components
- **TableContainer** - Used in all table components across the application
- **TableCellContainer** - Used in subject identifier tables
- **Actions** - Used in integrations and conditional access profile tables, context type detail containers
- **TextWithTooltip** - Used in tables and forms across multiple components

### Layout and Navigation Components
- **Tab** - Used in override manager page
- **Tabs** - Used in override manager page
- **Card** - Used in subject identifier detail containers
- **Tooltip** - Used in subject identifier detail containers

### Container Components
- **LoaderContainer** - Used in app layout
- **ToastContainer** - Used in app layout

---

## Utility Functions (19)

### Form Utilities (4)
- **defaultFormProps** - Used in forms across conditional access profiles and override manager
- **defaultFormPropTypes** - Used in forms across conditional access profiles and override manager
- **defaultValidationDetail** - Used in helper files across integrations, conditional access profiles, and override manager
- **mergeFormValues** - Used in forms across conditional access profiles and override manager

### API Notification Functions (3)
- **getApiDELETENotificationOptions** - Used in CRUD operations
- **getApiPOSTNotificationOptions** - Used in CRUD operations
- **getApiPUTNotificationOptions** - Used in CRUD operations

### Data Processing Functions (4)
- **getCalendarDDList** - Used in signal history page
- **getDropDownList** - Used in signal history selectors
- **getErrorMessageFromApiResponse** - Used in API helpers
- **getTimeRange** - Used in signal history constants

### DOM and Setup Functions (8)
- **API_METHOD** - Used in global setup
- **createDOMElement** - Used in global setup
- **isDOMElementPresent** - Used in global setup
- **setApiMethodMessageOption** - Used in global setup
- **setFloatingPortalRootId** - Used in global setup
- **setModalRootId** - Used in global setup
- **setToastPortalRootId** - Used in global setup
- **updateUseApiCallFunctionsRegistry** - Used in global setup

---

## SCSS Import (1)

### Main Stylesheet
```scss
@import '@zscaler/zui-component-library/dist/scss/main.scss';
```
- **Location:** `src/scss/vendors/_zui-component-library.scss`
- **Purpose:** Imports the complete ZUI component library styles

---

## File Distribution

### Main Pages (4)
- `src/pages/SignalHistoryPage.tsx`
- `src/pages/OverrideManagerPage.tsx`
- `src/pages/IntegrationsPage.tsx`
- `src/pages/ProfilesPage.tsx`

### Component Categories

#### Signal History Components (2 files)
- `src/components/signal-history/Filters.tsx`
- Signal history constants and selectors

#### Integration Components (8 files)
- `src/components/integrations/Table.tsx`
- `src/components/integrations/silver-fort/SilverFortModal.tsx`
- `src/components/integrations/crowd-strike/CrowdStrikeModal.tsx`
- `src/components/integrations/okta/OktaModal.tsx`
- `src/components/integrations/microsoft-defender/MicrosoftDefenderModal.tsx`
- Various integration helper files

#### Conditional Access Profile Components (6 files)
- `src/components/conditional-access-profile/Actions.tsx`
- `src/components/conditional-access-profile/CRUD.tsx`
- `src/components/conditional-access-profile/Form.tsx`
- `src/components/conditional-access-profile/QueryBuilder.tsx`
- `src/components/conditional-access-profile/Table.tsx`
- `src/components/conditional-access-profile/helper.tsx`

#### Override Manager Components (15 files)
- Subject Identifier components (8 files)
- Context Type components (6 files)
- Common Actions component (1 file)

### System Files (5 files)
- `src/zuxp-layout/AppLayout.tsx`
- `src/ducks/global/setupWithContext.ts`
- `src/ducks/global/apiHelpers.ts`
- `src/ducks/signal-history/constants.ts`
- `src/ducks/signal-history/selectors.ts`

---

## Usage Patterns

### Most Frequently Used Components
1. **TableContainer** - Used in all table implementations
2. **Button** - Used across forms and actions
3. **Modal/ModalBody/ModalHeader** - Used in all modal implementations
4. **Input** - Used in most form implementations
5. **DropDown** - Used in filters and form selections

### Component Combinations
- **Modal Suite**: Modal + ModalBody + ModalHeader + ModalFooter
- **Form Suite**: Input + Field + Button + defaultFormProps + mergeFormValues
- **Table Suite**: TableContainer + Actions + TextWithTooltip
- **Notification Suite**: getApi*NotificationOptions functions

---

## Dependencies

### Package.json Entry
```json
"@zscaler/zui-component-library": "^3.2.1"
```

### TypeScript Declarations
- Custom type definitions in `src/types/zui-component-library.d.ts`
- Provides type safety for all imported components and functions

---

## Conclusion

The adaptive access pages package makes extensive use of the ZUI component library, leveraging 46 different imports across 26 UI components and 19 utility functions. This represents a comprehensive integration covering all major UI patterns including forms, tables, modals, navigation, and system setup functionality.

The usage spans across all major feature areas:
- Signal History management
- Integration configurations
- Conditional Access Profiles
- Override Manager functionality
- Global application setup and layout

This analysis confirms that the ZUI component library is a critical dependency providing the foundational UI components and utilities for the entire adaptive access pages package.
